/* eslint-disable @typescript-eslint/no-var-requires */
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { AppModule } from './app.module';
import * as hbs from 'hbs';
import * as session from 'express-session';
import { Pool } from 'pg';
import * as dotenv from 'dotenv';
const ConnectPgSimple = require('connect-pg-simple')(session);

// Load environment variables
dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  app.useStaticAssets(join(__dirname, '..', 'public'));
  app.setBaseViewsDir(join(__dirname, '..', 'views'));
  app.setViewEngine('hbs');
  hbs.registerPartials(join(__dirname, '..', '/views/partials'));

  const pgPool = new Pool({
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 5432,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    ssl: { rejectUnauthorized: false },
  });

  const PgStore = new ConnectPgSimple({
    pool: pgPool,
    tableName: 'session',
    createTableIfMissing: true,
  });

  app.use(
    session({
      store: PgStore,
      secret: process.env.SESSION_SECRET || 'nomad-secret-!@#',
      name: 'nomad.sid',
      resave: false,
      saveUninitialized: false,
      unset: 'destroy',
      cookie: {
        maxAge: 14 * 24 * 60 * 60 * 1000, // 14 days in milliseconds
        secure: process.env.NODE_ENV === 'production', // Secure cookie in production
      },
    }),
  );

  const port = process.env.PORT || 3000;
  app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
  });
}
bootstrap();
